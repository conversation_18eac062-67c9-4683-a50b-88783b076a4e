"use client";
import {
  Box,
  Button,
  Flex,
  HStack,
  IconButton,
  Input,
  Stack,
  Table,
  Text,
  Badge,
} from "@chakra-ui/react";
import { LuSearch } from "react-icons/lu";

// Dados fictícios para a estrutura
const structureData = [
  {
    id: 1,
    name: "Diretoria Executiva",
    code: "DIR-001",
    level: 1,
    parent: null,
    status: "Ativo",
    employees: 5,
    createdAt: "2024-01-15",
  },
  {
    id: 2,
    name: "Gerência de TI",
    code: "GER-002",
    level: 2,
    parent: "Diretoria Executiva",
    status: "Ativo",
    employees: 12,
    createdAt: "2024-01-20",
  },
  {
    id: 3,
    name: "Coordenação de Desenvolvimento",
    code: "COO-003",
    level: 3,
    parent: "Gerência de TI",
    status: "Ativo",
    employees: 8,
    createdAt: "2024-02-01",
  },
  {
    id: 4,
    name: "<PERSON><PERSON><PERSON>ncia Financeira",
    code: "GER-004",
    level: 2,
    parent: "Diretoria Executiva",
    status: "Ativo",
    employees: 15,
    createdAt: "2024-01-25",
  },
  {
    id: 5,
    name: "Coordenação de Análise",
    code: "COO-005",
    level: 3,
    parent: "Gerência Financeira",
    status: "Inativo",
    employees: 6,
    createdAt: "2024-02-10",
  },
];

export default function Structure() {
  return (
    <Flex flex={1} position={"relative"} overflow={"hidden"} p={4}>
      <Stack w="100%" gap={6}>
        {/* Header */}
        <Text fontSize="2xl" fontWeight="bold" color="white">
          Estrutura Organizacional
        </Text>

        {/* Search and Filters */}
        <HStack gap={4} justify="space-between">
          <Box position="relative" flex={1} maxW="400px">
            <Input
              placeholder="Buscar por nome ou código..."
              bg="gray.800"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
            />
            <Box
              position="absolute"
              right={3}
              top="50%"
              transform="translateY(-50%)"
              color="gray.400"
            >
              <LuSearch />
            </Box>
          </Box>
          <Button bgColor={"#a5854a"} color={"#fff"} size="md">
            Nova Estrutura
          </Button>
        </HStack>

        {/* Table */}
        <Box
          bg="gray.800"
          borderRadius="lg"
          border="1px solid"
          borderColor="gray.600"
          overflow="hidden"
        >
          <Table.Root size="md" variant="outline">
            <Table.Header bg="gray.700">
              <Table.Row>
                <Table.ColumnHeader color="gray.200" fontWeight="semibold">
                  Seguimento
                </Table.ColumnHeader>
                <Table.ColumnHeader color="gray.200" fontWeight="semibold">
                  Superintendência
                </Table.ColumnHeader>
                <Table.ColumnHeader color="gray.200" fontWeight="semibold">
                  Gerências
                </Table.ColumnHeader>
                <Table.ColumnHeader color="gray.200" fontWeight="semibold">
                  Cargos
                </Table.ColumnHeader>
                <Table.ColumnHeader color="gray.200" fontWeight="semibold">
                  Ações
                </Table.ColumnHeader>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {structureData.map((item) => (
                <Table.Row key={item.id} _hover={{ bg: "gray.700" }}>
                  <Table.Cell>
                    <Text color="white" fontWeight="medium">
                      {item.name}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300" fontFamily="mono">
                      {item.code}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Badge
                      colorScheme={
                        item.level === 1
                          ? "purple"
                          : item.level === 2
                          ? "blue"
                          : "green"
                      }
                      variant="subtle"
                    >
                      Nível {item.level}
                    </Badge>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300">{item.parent || "-"}</Text>
                  </Table.Cell>
                  <Table.Cell>
                    <HStack gap={2}>
                      <IconButton
                        aria-label="Editar"
                        size="sm"
                        variant="solid"
                        colorPalette="white"
                      />
                      <IconButton
                        aria-label="Excluir"
                        size="sm"
                        variant="solid"
                        colorPalette="yellow"
                      />
                    </HStack>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table.Root>
        </Box>
      </Stack>
    </Flex>
  );
}
