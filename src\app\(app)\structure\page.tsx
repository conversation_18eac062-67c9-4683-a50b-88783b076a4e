"use client";
import Default<PERSON>utton from "@/components/global/buttons/button";
import BasicModal from "@/components/global/modal/basic-modal";
import {
  Box,
  Button,
  Flex,
  HStack,
  IconButton,
  Input,
  Stack,
  Table,
  Text,
  Badge,
  VStack,
  Field,
} from "@chakra-ui/react";
import { LuPencil, LuPlus, LuSearch, LuTrash2 } from "react-icons/lu";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";

// Dados fictícios para a estrutura
const structureData = [
  {
    id: 1,
    secureId: "f6c78f9d-4b65-4f2f-92a8-1173b34da1c6",
    segment: {
      name: "Comercial",
      secureId: "a1e23f4e-9c1f-4a0e-8a12-54675dfc4a91",
    },
    superintendency: {
      name: "Superintendência A",
      secureId: "b2f34e5f-8d21-4b4f-b124-219fd41b9a02",
    },
    management: {
      name: "Gerência de Operações",
      secureId: "c3d45f6a-7e32-4c2d-a9a6-776fde77cde3",
    },
    position: {
      name: "Analista de Vendas",
      secureId: "d4a56f7b-6f43-4d0e-a321-9912ccf9b014",
    },
    createdAt: "2024-02-01T09:00:00Z",
    updatedAt: "2024-02-01T09:00:00Z",
  },
  {
    id: 2,
    secureId: "a7d89f0e-3c76-4f3d-81b9-221bfa5cb2d7",
    segment: {
      name: "Tecnologia",
      secureId: "e5b67f8c-5a54-4e1f-bc88-34f91a0ccf25",
    },
    superintendency: {
      name: "Superintendência A",
      secureId: "b2f34e5f-8d21-4b4f-b124-219fd41b9a02",
    },
    management: {
      name: "Gerência de Operações",
      secureId: "c3d45f6a-7e32-4c2d-a9a6-776fde77cde3",
    },
    position: {
      name: "Analista de Vendas",
      secureId: "d4a56f7b-6f43-4d0e-a321-9912ccf9b014",
    },
    createdAt: "2024-02-01T09:05:00Z",
    updatedAt: "2024-02-01T09:05:00Z",
  },
  {
    id: 3,
    secureId: "b8e90f1f-2d87-4f4e-70ca-332cdb6ec3e8",
    segment: {
      name: "Comercial",
      secureId: "a1e23f4e-9c1f-4a0e-8a12-54675dfc4a91",
    },
    superintendency: {
      name: "Superintendência A",
      secureId: "b2f34e5f-8d21-4b4f-b124-219fd41b9a02",
    },
    management: {
      name: "Gerência de Operações",
      secureId: "c3d45f6a-7e32-4c2d-a9a6-776fde77cde3",
    },
    position: {
      name: "Analista de Vendas",
      secureId: "d4a56f7b-6f43-4d0e-a321-9912ccf9b014",
    },
    createdAt: "2024-02-01T09:10:00Z",
    updatedAt: "2024-02-01T09:10:00Z",
  },
  {
    id: 4,
    secureId: "c9f01f2a-1e98-4f5f-61db-443dec7fd4f9",
    segment: {
      name: "Tecnologia",
      secureId: "e5b67f8c-5a54-4e1f-bc88-34f91a0ccf25",
    },
    superintendency: {
      name: "Superintendência A",
      secureId: "b2f34e5f-8d21-4b4f-b124-219fd41b9a02",
    },
    management: {
      name: "Gerência de Operações",
      secureId: "c3d45f6a-7e32-4c2d-a9a6-776fde77cde3",
    },
    position: {
      name: "Analista de Vendas",
      secureId: "d4a56f7b-6f43-4d0e-a321-9912ccf9b014",
    },
    createdAt: "2024-02-01T09:15:00Z",
    updatedAt: "2024-02-01T09:15:00Z",
  },
  {
    id: 5,
    secureId: "da012f3b-0fa9-4f6f-52ec-554efd8fe5g0",
    segment: {
      name: "Comercial",
      secureId: "a1e23f4e-9c1f-4a0e-8a12-54675dfc4a91",
    },
    superintendency: {
      name: "Superintendência A",
      secureId: "b2f34e5f-8d21-4b4f-b124-219fd41b9a02",
    },
    management: {
      name: "Gerência de Operações",
      secureId: "c3d45f6a-7e32-4c2d-a9a6-776fde77cde3",
    },
    position: {
      name: "Analista de Vendas",
      secureId: "d4a56f7b-6f43-4d0e-a321-9912ccf9b014",
    },
    createdAt: "2024-02-01T09:20:00Z",
    updatedAt: "2024-02-01T09:20:00Z",
  },
];

// Form schema for structure
const NewStructureSchema = yup.object().shape({
  segmentName: yup
    .string()
    .min(3, "O nome do segmento deve ter no mínimo 3 caracteres")
    .max(50, "O nome do segmento deve ter no máximo 50 caracteres")
    .required("O nome do segmento é obrigatório"),
  superintendencyName: yup
    .string()
    .min(3, "O nome da superintendência deve ter no mínimo 3 caracteres")
    .max(50, "O nome da superintendência deve ter no máximo 50 caracteres")
    .required("O nome da superintendência é obrigatório"),
  managementName: yup
    .string()
    .min(3, "O nome da gerência deve ter no mínimo 3 caracteres")
    .max(50, "O nome da gerência deve ter no máximo 50 caracteres")
    .required("O nome da gerência é obrigatório"),
  positionName: yup
    .string()
    .min(3, "O nome do cargo deve ter no mínimo 3 caracteres")
    .max(50, "O nome do cargo deve ter no máximo 50 caracteres")
    .required("O nome do cargo é obrigatório"),
});

type NewStructureFormData = yup.InferType<typeof NewStructureSchema>;

export default function Structure() {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedStructure, setSelectedStructure] = useState<
    (typeof structureData)[0] | null
  >(null);

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<NewStructureFormData>({
    resolver: yupResolver(NewStructureSchema),
    defaultValues: {
      segmentName: "",
      superintendencyName: "",
      managementName: "",
      positionName: "",
    },
  });

  const handleOpenAddModal = () => {
    reset();
    setIsAddModalOpen(true);
  };

  const handleCloseAddModal = () => {
    reset();
    setIsAddModalOpen(false);
  };

  const handleOpenEditModal = (structure: (typeof structureData)[0]) => {
    setSelectedStructure(structure);
    setValue("segmentName", structure.segment.name);
    setValue("superintendencyName", structure.superintendency.name);
    setValue("managementName", structure.management.name);
    setValue("positionName", structure.position.name);
    setIsEditModalOpen(true);
  };

  const handleCloseEditModal = () => {
    reset();
    setSelectedStructure(null);
    setIsEditModalOpen(false);
  };

  const handleOpenDeleteModal = (structure: (typeof structureData)[0]) => {
    setSelectedStructure(structure);
    setIsDeleteModalOpen(true);
  };

  const handleCloseDeleteModal = () => {
    setSelectedStructure(null);
    setIsDeleteModalOpen(false);
  };

  const handleAddStructure = async (data: NewStructureFormData) => {
    console.log("New Structure Data:", data);
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        alert("Estrutura adicionada com sucesso!");
        handleCloseAddModal();
        resolve();
      }, 500);
    });
  };

  const handleEditStructure = async (data: NewStructureFormData) => {
    console.log(
      "Edit Structure Data:",
      data,
      "for structure:",
      selectedStructure?.secureId
    );
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        alert("Estrutura editada com sucesso!");
        handleCloseEditModal();
        resolve();
      }, 500);
    });
  };

  const handleDeleteStructure = async () => {
    console.log("Delete Structure:", selectedStructure?.secureId);
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        alert("Estrutura excluída com sucesso!");
        handleCloseDeleteModal();
        resolve();
      }, 500);
    });
  };
  return (
    <Flex flex={1} position={"relative"} overflow={"hidden"} p={4}>
      <Stack w="100%" gap={6}>
        {/* Header */}
        <Text fontSize="2xl" fontWeight="bold" color="white">
          Estrutura Organizacional
        </Text>

        {/* Search and Filters */}
        <HStack gap={4} justify="space-between">
          <Box position="relative" flex={1} maxW="400px">
            <Input
              placeholder="Buscar por nome ou código..."
              bg="gray.800"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
            />
            <Box
              position="absolute"
              right={3}
              top="50%"
              transform="translateY(-50%)"
              color="gray.400"
            >
              <LuSearch />
            </Box>
          </Box>
          <DefaultButton onClick={handleOpenAddModal} size="md">
            <LuPlus />
            Adicionar Estrutura
          </DefaultButton>
        </HStack>

        {/* Table */}
        <Box
          bg="gray.800"
          borderRadius="lg"
          border="1px solid"
          borderColor="gray.600"
          overflow="hidden"
        >
          <Table.Root size="md" variant="outline">
            <Table.Header bg="gray.700">
              <Table.Row>
                <Table.ColumnHeader color="WHITE" fontWeight="bold">
                  Seguimento
                </Table.ColumnHeader>
                <Table.ColumnHeader color="WHITE" fontWeight="bold">
                  Superintendência
                </Table.ColumnHeader>
                <Table.ColumnHeader color="WHITE" fontWeight="bold">
                  Gerências
                </Table.ColumnHeader>
                <Table.ColumnHeader color="WHITE" fontWeight="bold">
                  Cargos
                </Table.ColumnHeader>
                <Table.ColumnHeader
                  color="white"
                  fontWeight="bold"
                  display={"flex"}
                  justifyContent={"center"}
                  borderBottom={"1px solid #3f3f46"}
                >
                  Ações
                </Table.ColumnHeader>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {structureData.map((item) => (
                <Table.Row key={item.id} _hover={{ bg: "gray.700" }}>
                  <Table.Cell>
                    <Text color="gray.300">{item.segment.name}</Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300">{item.superintendency.name}</Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300">{item.management.name}</Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300">{item.position.name}</Text>
                  </Table.Cell>
                  <Table.Cell>
                    <HStack gap={2} justifyContent={"center"}>
                      <DefaultButton
                        tooltipContent="Editar"
                        buttonColor="#156082"
                        size="sm"
                        onClick={() => handleOpenEditModal(item)}
                      >
                        <LuPencil />
                      </DefaultButton>
                      <DefaultButton
                        tooltipContent="Excluir"
                        buttonColor="red.500"
                        size="sm"
                        onClick={() => handleOpenDeleteModal(item)}
                      >
                        <LuTrash2 />
                      </DefaultButton>
                    </HStack>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table.Root>
        </Box>
      </Stack>

      {/* Add Structure Modal */}
      <BasicModal
        open={isAddModalOpen}
        setOpen={setIsAddModalOpen}
        title="Adicionar Nova Estrutura"
        size="lg"
        asForm={true}
        handleSubmit={handleSubmit(handleAddStructure)}
        isSubmitting={isSubmitting}
        confirmText="Adicionar"
        cancelText="Cancelar"
        placement="center"
      >
        <VStack gap={4} align="stretch">
          <Field.Root invalid={!!errors.segmentName}>
            <Field.Label color="white">Nome do Segmento</Field.Label>
            <Input
              placeholder="Digite o nome do segmento"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("segmentName")}
            />
            <Field.ErrorText>{errors.segmentName?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.superintendencyName}>
            <Field.Label color="white">Nome da Superintendência</Field.Label>
            <Input
              placeholder="Digite o nome da superintendência"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("superintendencyName")}
            />
            <Field.ErrorText>
              {errors.superintendencyName?.message}
            </Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.managementName}>
            <Field.Label color="white">Nome da Gerência</Field.Label>
            <Input
              placeholder="Digite o nome da gerência"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("managementName")}
            />
            <Field.ErrorText>{errors.managementName?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.positionName}>
            <Field.Label color="white">Nome do Cargo</Field.Label>
            <Input
              placeholder="Digite o nome do cargo"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("positionName")}
            />
            <Field.ErrorText>{errors.positionName?.message}</Field.ErrorText>
          </Field.Root>
        </VStack>
      </BasicModal>

      {/* Edit Structure Modal */}
      <BasicModal
        open={isEditModalOpen}
        setOpen={setIsEditModalOpen}
        title="Editar Estrutura"
        size="lg"
        asForm={true}
        handleSubmit={handleSubmit(handleEditStructure)}
        isSubmitting={isSubmitting}
        confirmText="Salvar"
        cancelText="Cancelar"
        placement="center"
      >
        <VStack gap={4} align="stretch">
          <Field.Root invalid={!!errors.segmentName}>
            <Field.Label color="white">Nome do Segmento</Field.Label>
            <Input
              placeholder="Digite o nome do segmento"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("segmentName")}
            />
            <Field.ErrorText>{errors.segmentName?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.superintendencyName}>
            <Field.Label color="white">Nome da Superintendência</Field.Label>
            <Input
              placeholder="Digite o nome da superintendência"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("superintendencyName")}
            />
            <Field.ErrorText>
              {errors.superintendencyName?.message}
            </Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.managementName}>
            <Field.Label color="white">Nome da Gerência</Field.Label>
            <Input
              placeholder="Digite o nome da gerência"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("managementName")}
            />
            <Field.ErrorText>{errors.managementName?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.positionName}>
            <Field.Label color="white">Nome do Cargo</Field.Label>
            <Input
              placeholder="Digite o nome do cargo"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("positionName")}
            />
            <Field.ErrorText>{errors.positionName?.message}</Field.ErrorText>
          </Field.Root>
        </VStack>
      </BasicModal>

      {/* Delete Structure Modal */}
      <BasicModal
        open={isDeleteModalOpen}
        setOpen={setIsDeleteModalOpen}
        title="Excluir Estrutura"
        size="sm"
        handleConfirm={handleDeleteStructure}
        confirmText="Excluir"
        cancelText="Cancelar"
        placement="center"
        confirmButtonColor="red.500"
      >
        <VStack gap={4} align="center">
          <Text fontSize="md" color="white" textAlign="center">
            Você tem certeza que deseja excluir esta estrutura organizacional?
          </Text>
          <VStack gap={2} align="center">
            <Text fontSize="sm" color="gray.300">
              <Text as="span" fontWeight="bold">
                Segmento:
              </Text>{" "}
              {selectedStructure?.segment.name}
            </Text>
            <Text fontSize="sm" color="gray.300">
              <Text as="span" fontWeight="bold">
                Superintendência:
              </Text>{" "}
              {selectedStructure?.superintendency.name}
            </Text>
            <Text fontSize="sm" color="gray.300">
              <Text as="span" fontWeight="bold">
                Gerência:
              </Text>{" "}
              {selectedStructure?.management.name}
            </Text>
            <Text fontSize="sm" color="gray.300">
              <Text as="span" fontWeight="bold">
                Cargo:
              </Text>{" "}
              {selectedStructure?.position.name}
            </Text>
          </VStack>
          <Text fontSize="sm" color="gray.400" textAlign="center">
            Esta ação não pode ser desfeita.
          </Text>
        </VStack>
      </BasicModal>
    </Flex>
  );
}
