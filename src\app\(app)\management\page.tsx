"use client";
import {
  Box,
  Flex,
  Grid,
  GridItem,
  HStack,
  Image,
  Text,
  VStack,
} from "@chakra-ui/react";

export default function Management() {
  return (
    <Flex flex={1} position={"relative"} overflow={"hidden"}>
      <Box
        position="absolute"
        top={0}
        left={0}
        transform="translateY(50%)"
        w={"100%"}
        h={"100%"}
        bgImg="url(/images/login/padraoBG.svg)"
        bgRepeat="no-repeat"
        bgPos="center bottom"
        bgSize="contain"
        clipPath="inset(0 0 50% 0)"
      />
      <Grid
        templateColumns="repeat(2, 1fr)"
        gap={1}
        w={"100vw"}
        h="100vh"
        position={"relative"}
      >
        <GridItem>
          <VStack height="100vh" p={8} gap={8} alignItems="center">
            <HStack
              w={"100%"}
              justifyContent="center"
              alignItems="center"
              gap={8}
            >
              <Image
                src="/images/logoBancoABC.svg"
                alt="Banco ABC"
                w="100px"
                h="auto"
              />
              <Text fontSize={"40px"}>Dashboard Assessment ABC BRASIL</Text>
            </HStack>
            <Flex
              as="main"
              w="100%"
              flex={1}
              alignItems="center"
              justifyContent="center"
            >
              <VStack alignItems="center">
                <Text
                  fontSize="32px"
                  letterSpacing={"wider"}
                  alignSelf={"start"}
                  color="#a6864a"
                >
                  INTRODUÇÃO
                </Text>
                <Text fontSize="24px" textAlign="start" maxW="xl" mb={"100px"}>
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla
                  nec purus feugiat, varius lorem eu, pellentesque elit. Sed ut
                  perspiciatis unde omnis iste natus error sit voluptatem
                  accusantium doloremque laudantium.
                </Text>
              </VStack>
            </Flex>
          </VStack>
        </GridItem>
        <GridItem>
          <Box
            // hideBelow={"xl"}
            bgImage="url(/images/login/bg-02.png)"
            w="100vh"
            h="100vh"
            bgRepeat="no-repeat"
            bgSize="cover"
            position="relative"
            _after={{
              content: '""',
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background:
                "linear-gradient(to right, rgba(35,34,34,1) 0%, transparent 3%)",
            }}
          />
        </GridItem>
      </Grid>
    </Flex>
  );
}
