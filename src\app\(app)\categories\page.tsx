"use client";
import {
  <PERSON>,
  But<PERSON>,
  <PERSON>lex,
  H<PERSON>tack,
  Icon<PERSON>utton,
  Input,
  Stack,
  Table,
  Text,
  Badge,
} from "@chakra-ui/react";
import { LuTrash2, LuPlus, LuSearch } from "react-icons/lu";

// Dados fictícios para categorias
const categoriesData = [
  {
    id: 1,
    name: "Tecnologia",
    code: "TEC-001",
    description: "Categoria para departamentos de tecnologia",
    color: "#3182ce",
    status: "Ativo",
    departments: 3,
    createdAt: "2024-01-10",
  },
  {
    id: 2,
    name: "Financeiro",
    code: "FIN-002",
    description: "Categoria para departamentos financeiros",
    color: "#38a169",
    status: "Ativo",
    departments: 2,
    createdAt: "2024-01-15",
  },
  {
    id: 3,
    name: "Recursos Humanos",
    code: "RH-003",
    description: "Categoria para departamentos de RH",
    color: "#d69e2e",
    status: "Ativo",
    departments: 1,
    createdAt: "2024-01-20",
  },
  {
    id: 4,
    name: "Marketing",
    code: "MKT-004",
    description: "Categoria para departamentos de marketing",
    color: "#e53e3e",
    status: "Inativo",
    departments: 0,
    createdAt: "2024-02-01",
  },
  {
    id: 5,
    name: "Operações",
    code: "OPE-005",
    description: "Categoria para departamentos operacionais",
    color: "#805ad5",
    status: "Ativo",
    departments: 4,
    createdAt: "2024-02-05",
  },
];

export default function Categories() {
  return (
    <Flex flex={1} position={"relative"} overflow={"hidden"} p={4}>
      <Stack w="100%" gap={6}>
        {/* Header */}
        <Text fontSize="2xl" fontWeight="bold" color="white">
          Categorias
        </Text>

        {/* Search and Filters */}
        <HStack gap={4} justify="space-between">
          <Box position="relative" flex={1} maxW="400px">
            <Input
              placeholder="Buscar..."
              bg="gray.800"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
            />
            <Box
              position="absolute"
              right={3}
              top="50%"
              transform="translateY(-50%)"
              color="gray.400"
            >
              <LuSearch />
            </Box>
          </Box>
          <Button bgColor={"#a5854a"} color={"#fff"} size="md">
            Nova Categoria
          </Button>
        </HStack>

        {/* Table */}
        <Box
          bg="gray.800"
          borderRadius="lg"
          border="1px solid"
          borderColor="gray.600"
          overflow="hidden"
        >
          <Table.Root size="md" variant="outline">
            <Table.Header bg="gray.700">
              <Table.Row>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Nome
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Descrição
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Tipo
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Ações
                </Table.ColumnHeader>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {categoriesData.map((item) => (
                <Table.Row key={item.id} _hover={{ bg: "gray.700" }}>
                  <Table.Cell>
                    <Text color="white" fontWeight="medium">
                      {item.name}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300" fontFamily="mono">
                      {item.code}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300" maxW="200px">
                      {item.description}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <HStack gap={2}>
                      <IconButton
                        aria-label="Editar"
                        size="sm"
                        variant="solid"
                        colorPalette="yellow"
                      />
                      <IconButton
                        aria-label="Excluir"
                        size="sm"
                        variant="solid"
                        colorPalette="white"
                      />
                    </HStack>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table.Root>
        </Box>
      </Stack>
    </Flex>
  );
}
