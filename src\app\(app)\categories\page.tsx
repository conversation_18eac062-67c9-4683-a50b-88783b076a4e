"use client";
import DefaultButton from "@/components/global/buttons/button";
import BasicModal from "@/components/global/modal/basic-modal";
import { translateCategoryType } from "@/utils/formatters/formatCategoriesType";
import {
  Box,
  Button,
  Flex,
  HStack,
  IconButton,
  Input,
  Stack,
  Table,
  Text,
  Badge,
  useDisclosure,
  VStack,
  Field,
  Combobox,
  useFilter,
  useListCollection,
  Portal,
} from "@chakra-ui/react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useState } from "react";
import { useForm } from "react-hook-form";
import {
  LuTrash2,
  LuPlus,
  LuSearch,
  LuDelete,
  LuPen,
  LuPenLine,
  LuPencil,
} from "react-icons/lu";
import * as yup from "yup";

// Dados fictícios para categorias
const categoriesData = [
  {
    id: 1,
    secureId: "a1e23f4e-9c1f-4a0e-8a12-54675dfc4a91",
    name: "Comercial",
    type: "segment",
    createdAt: "2024-01-10T08:00:00Z",
    updatedAt: "2024-01-10T08:00:00Z",
  },
  {
    id: 2,
    secureId: "b2f34e5f-8d21-4b4f-b124-219fd41b9a02",
    name: "Superintendência A",
    type: "superintendency",
    createdAt: "2024-01-10T08:05:00Z",
    updatedAt: "2024-01-10T08:05:00Z",
  },
  {
    id: 3,
    secureId: "c3d45f6a-7e32-4c2d-a9a6-776fde77cde3",
    name: "Gerência de Operações",
    type: "management",
    createdAt: "2024-01-10T08:10:00Z",
    updatedAt: "2024-01-10T08:10:00Z",
  },
  {
    id: 4,
    secureId: "d4a56f7b-6f43-4d0e-a321-9912ccf9b014",
    name: "Analista de Vendas",
    type: "position",
    createdAt: "2024-01-10T08:15:00Z",
    updatedAt: "2024-01-10T08:15:00Z",
  },
  {
    id: 5,
    secureId: "e5b67f8c-5a54-4e1f-bc88-34f91a0ccf25",
    name: "Tecnologia",
    type: "segment",
    createdAt: "2024-01-10T08:20:00Z",
    updatedAt: "2024-01-10T08:20:00Z",
  },
];

const categoriesTypes = [
  { label: "Segmento", value: "segment" },
  { label: "Superintendência", value: "superintendency" },
  { label: "Gerência", value: "management" },
  { label: "Cargo", value: "position" },
];

const NewCategorySchema = yup.object().shape({
  name: yup
    .string()
    .min(3, "O nome deve ter no mínimo 3 caracteres")
    .max(50, "O nome deve ter no máximo 50 caracteres")
    .required("O nome da categoria é obrigatório"),
  type: yup
    .string()
    .oneOf(
      ["segment", "superintendency", "management", "position"],
      "Tipo de categoria inválido"
    )
    .required("O tipo da categoria é obrigatório"),
});

type NewCategoryFormData = yup.InferType<typeof NewCategorySchema>;

export default function Categories() {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<
    (typeof categoriesData)[0] | null
  >(null);

  const { contains } = useFilter({ sensitivity: "base" });

  const { collection, filter } = useListCollection({
    initialItems: categoriesData,
    filter: contains,
  });

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<NewCategoryFormData>({
    resolver: yupResolver(NewCategorySchema),
    defaultValues: {
      name: "",
      type: undefined,
    },
  });

  const handleOpenAddModal = () => {
    reset();
    setIsAddModalOpen(true);
  };

  const handleCloseAddModal = () => {
    reset();
    setIsAddModalOpen(false);
  };

  const handleOpenEditModal = (category: (typeof categoriesData)[0]) => {
    setSelectedCategory(category);
    setValue("name", category.name);
    setValue("type", category.type as any);
    setIsEditModalOpen(true);
  };

  const handleCloseEditModal = () => {
    reset();
    setSelectedCategory(null);
    setIsEditModalOpen(false);
  };

  const handleOpenDeleteModal = (category: (typeof categoriesData)[0]) => {
    setSelectedCategory(category);
    setIsDeleteModalOpen(true);
  };

  const handleCloseDeleteModal = () => {
    setSelectedCategory(null);
    setIsDeleteModalOpen(false);
  };

  const handleAddCategory = async (data: NewCategoryFormData) => {
    console.log("New Category Data:", data);
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        alert("Categoria adicionada com sucesso!");
        handleCloseAddModal();
        resolve();
      }, 500);
    });
  };

  const handleEditCategory = async (data: NewCategoryFormData) => {
    console.log(
      "Edit Category Data:",
      data,
      "for category:",
      selectedCategory?.secureId
    );
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        alert("Categoria editada com sucesso!");
        handleCloseEditModal();
        resolve();
      }, 500);
    });
  };

  const handleDeleteCategory = async () => {
    console.log("Delete Category:", selectedCategory?.secureId);
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        alert("Categoria excluída com sucesso!");
        handleCloseDeleteModal();
        resolve();
      }, 500);
    });
  };

  return (
    <Flex flex={1} position={"relative"} overflow={"hidden"} p={4}>
      <Stack w="100%" gap={6}>
        {/* Header */}
        <Text fontSize="2xl" fontWeight="bold" color="white">
          Categorias
        </Text>

        {/* Search and Filters */}
        <HStack gap={4} justify="space-between">
          <Box position="relative" flex={1} maxW="400px">
            <Input
              placeholder="Buscar..."
              bg="gray.800"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
            />
            <Box
              position="absolute"
              right={3}
              top="50%"
              transform="translateY(-50%)"
              color="gray.400"
            >
              <LuSearch />
            </Box>
          </Box>
          <DefaultButton onClick={handleOpenAddModal} size="md">
            <LuPlus />
            Adicionar Categoria
          </DefaultButton>
        </HStack>

        {/* Table */}
        <Box
          bg="gray.800"
          borderRadius="lg"
          border="1px solid"
          borderColor="gray.600"
          overflow="hidden"
        >
          <Table.Root size="md" variant="outline">
            <Table.Header bg="gray.700">
              <Table.Row>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Nome
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Tipo
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Data de Criação
                </Table.ColumnHeader>
                <Table.ColumnHeader
                  color="white"
                  fontWeight="bold"
                  display={"flex"}
                  justifyContent={"center"}
                  borderBottom={"1px solid #3f3f46"}
                >
                  Ações
                </Table.ColumnHeader>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {categoriesData.map((item) => (
                <Table.Row key={item.id} _hover={{ bg: "gray.700" }}>
                  <Table.Cell>
                    <Text color="gray.300" fontWeight="medium">
                      {item.name}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300" maxW="200px">
                      {translateCategoryType(item.type)}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300" maxW="200px">
                      {item.createdAt}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <HStack gap={2} justifyContent={"center"}>
                      <DefaultButton
                        tooltipContent="Editar"
                        buttonColor="#156082"
                        size="sm"
                        onClick={() => handleOpenEditModal(item)}
                      >
                        <LuPencil />
                      </DefaultButton>
                      <DefaultButton
                        tooltipContent="Excluir"
                        buttonColor="red.500"
                        size="sm"
                        onClick={() => handleOpenDeleteModal(item)}
                      >
                        <LuTrash2 />
                      </DefaultButton>
                    </HStack>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table.Root>
        </Box>
      </Stack>
      {/* Add Category Modal */}
      <BasicModal
        open={isAddModalOpen}
        setOpen={setIsAddModalOpen}
        title="Adicionar Nova Categoria"
        size="md"
        asForm={true}
        handleSubmit={handleSubmit(handleAddCategory)}
        isSubmitting={isSubmitting}
        confirmText="Adicionar"
        cancelText="Cancelar"
        placement="center"
      >
        <VStack gap={4} align="stretch">
          <Field.Root invalid={!!errors.name}>
            <Field.Label color="white">Nome da Categoria</Field.Label>
            <Input
              placeholder="Digite o nome da categoria"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("name")}
            />
            <Field.ErrorText>{errors.name?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.type}>
            <Field.Label color="white">Tipo da Categoria</Field.Label>
            <Combobox.Root
              collection={collection}
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("type")}
            >
              <Combobox.Control>
                <Combobox.Input
                  placeholder="Selecione o tipo"
                  bg="gray.700"
                  border="1px solid"
                  borderColor="gray.600"
                  color="white"
                  _placeholder={{ color: "gray.400" }}
                  _focus={{
                    borderColor: "blue.400",
                    boxShadow: "0 0 0 1px #3182ce",
                  }}
                />
                <Combobox.IndicatorGroup>
                  <Combobox.ClearTrigger />
                  <Combobox.Trigger />
                </Combobox.IndicatorGroup>
              </Combobox.Control>
              <Portal>
                <Combobox.Positioner>
                  <Combobox.Content>
                    <Combobox.Empty>No items found</Combobox.Empty>
                    {collection.items.map((item) => (
                      <Combobox.Item item={item} key={item.value}>
                        {item.label}
                        <Combobox.ItemIndicator />
                      </Combobox.Item>
                    ))}
                  </Combobox.Content>
                </Combobox.Positioner>
              </Portal>
            </Combobox.Root>
            <Field.ErrorText>{errors.type?.message}</Field.ErrorText>
          </Field.Root>
        </VStack>
      </BasicModal>

      {/* Edit Category Modal */}
      <BasicModal
        open={isEditModalOpen}
        setOpen={setIsEditModalOpen}
        title="Editar Categoria"
        size="md"
        asForm={true}
        handleSubmit={handleSubmit(handleEditCategory)}
        isSubmitting={isSubmitting}
        confirmText="Salvar"
        cancelText="Cancelar"
        placement="center"
      >
        <VStack gap={4} align="stretch">
          <Field.Root invalid={!!errors.name}>
            <Field.Label color="white">Nome da Categoria</Field.Label>
            <Input
              placeholder="Digite o nome da categoria"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("name")}
            />
            <Field.ErrorText>{errors.name?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.type}>
            <Field.Label color="white">Tipo da Categoria</Field.Label>
            <Box
              as="select"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              borderRadius="md"
              p={2}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
                outline: "none",
              }}
              {...register("type")}
            >
              <option
                value=""
                style={{ backgroundColor: "#2D3748", color: "white" }}
              >
                Selecione o tipo
              </option>
              <option
                value="segment"
                style={{ backgroundColor: "#2D3748", color: "white" }}
              >
                Segmento
              </option>
              <option
                value="superintendency"
                style={{ backgroundColor: "#2D3748", color: "white" }}
              >
                Superintendência
              </option>
              <option
                value="management"
                style={{ backgroundColor: "#2D3748", color: "white" }}
              >
                Gerência
              </option>
              <option
                value="position"
                style={{ backgroundColor: "#2D3748", color: "white" }}
              >
                Cargo
              </option>
            </Box>
            <Field.ErrorText>{errors.type?.message}</Field.ErrorText>
          </Field.Root>
        </VStack>
      </BasicModal>

      {/* Delete Category Modal */}
      <BasicModal
        open={isDeleteModalOpen}
        setOpen={setIsDeleteModalOpen}
        title="Excluir Categoria"
        size="sm"
        handleConfirm={handleDeleteCategory}
        confirmText="Excluir"
        cancelText="Cancelar"
        placement="center"
        confirmButtonColor="red.500"
      >
        <VStack gap={4} align="center">
          <Text fontSize="md" color="white" textAlign="center">
            Você tem certeza que deseja excluir a categoria{" "}
            <Text as="span" fontWeight="bold" color="red.400">
              "{selectedCategory?.name}"
            </Text>
            ?
          </Text>
          <Text fontSize="sm" color="gray.400" textAlign="center">
            Esta ação não pode ser desfeita.
          </Text>
        </VStack>
      </BasicModal>
    </Flex>
  );
}
