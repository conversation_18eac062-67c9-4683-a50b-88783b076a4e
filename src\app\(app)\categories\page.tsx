"use client";
import DefaultButton from "@/components/global/buttons/button";
import BasicModal from "@/components/global/modal/basic-modal";
import { translateCategoryType } from "@/utils/formatters/formatCategoriesType";
import {
  Box,
  Button,
  Flex,
  HStack,
  IconButton,
  Input,
  Stack,
  Table,
  Text,
  Badge,
  useDisclosure,
  VStack,
} from "@chakra-ui/react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useState } from "react";
import { useForm } from "react-hook-form";
import {
  LuTrash2,
  LuPlus,
  LuSearch,
  LuDelete,
  LuPen,
  LuPenLine,
  LuPencil,
} from "react-icons/lu";
import * as yup from "yup";

// Dados fictícios para categorias
const categoriesData = [
  {
    id: 1,
    secureId: "a1e23f4e-9c1f-4a0e-8a12-54675dfc4a91",
    name: "Comercia<PERSON>",
    type: "segment",
    createdAt: "2024-01-10T08:00:00Z",
    updatedAt: "2024-01-10T08:00:00Z",
  },
  {
    id: 2,
    secureId: "b2f34e5f-8d21-4b4f-b124-219fd41b9a02",
    name: "Superintendência A",
    type: "superintendency",
    createdAt: "2024-01-10T08:05:00Z",
    updatedAt: "2024-01-10T08:05:00Z",
  },
  {
    id: 3,
    secureId: "c3d45f6a-7e32-4c2d-a9a6-776fde77cde3",
    name: "Gerência de Operações",
    type: "management",
    createdAt: "2024-01-10T08:10:00Z",
    updatedAt: "2024-01-10T08:10:00Z",
  },
  {
    id: 4,
    secureId: "d4a56f7b-6f43-4d0e-a321-9912ccf9b014",
    name: "Analista de Vendas",
    type: "position",
    createdAt: "2024-01-10T08:15:00Z",
    updatedAt: "2024-01-10T08:15:00Z",
  },
  {
    id: 5,
    secureId: "e5b67f8c-5a54-4e1f-bc88-34f91a0ccf25",
    name: "Tecnologia",
    type: "segment",
    createdAt: "2024-01-10T08:20:00Z",
    updatedAt: "2024-01-10T08:20:00Z",
  },
];

const NewCategorySchema = yup.object().shape({
  name: yup
    .string()
    .min(3, "O nome deve ter no mínimo 3 caracteres")
    .max(50, "O nome deve ter no máximo 50 caracteres")
    .required("O nome da categoria é obrigatório"),
  type: yup
    .string()
    .oneOf(
      ["segment", "superintendency", "management", "position"],
      "Tipo de categoria inválido"
    )
    .required("O tipo da categoria é obrigatório"),
});

type NewCategoryFormData = yup.InferType<typeof NewCategorySchema>;

export default function Categories() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<NewCategoryFormData>({
    resolver: yupResolver(NewCategorySchema),
    defaultValues: {
      name: "",
      type: undefined,
    },
  });

  const handleOpenModal = () => setIsModalOpen(true);
  const handleCloseModal = () => {
    reset();
    setIsModalOpen(false);
  };

  const handleAddCategory = async (data: NewCategoryFormData) => {
    console.log("New Category Data:", data);
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        alert("Categoria adicionada com sucesso!");
        reset();
        setIsModalOpen(false);
        resolve();
      }, 500);
    });
  };

  return (
    <Flex flex={1} position={"relative"} overflow={"hidden"} p={4}>
      <Stack w="100%" gap={6}>
        {/* Header */}
        <Text fontSize="2xl" fontWeight="bold" color="white">
          Categorias
        </Text>

        {/* Search and Filters */}
        <HStack gap={4} justify="space-between">
          <Box position="relative" flex={1} maxW="400px">
            <Input
              placeholder="Buscar..."
              bg="gray.800"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
            />
            <Box
              position="absolute"
              right={3}
              top="50%"
              transform="translateY(-50%)"
              color="gray.400"
            >
              <LuSearch />
            </Box>
          </Box>
          <DefaultButton onClick={handleOpenModal} size="md">
            <LuPlus />
            Adicionar Categoria
          </DefaultButton>
        </HStack>

        {/* Table */}
        <Box
          bg="gray.800"
          borderRadius="lg"
          border="1px solid"
          borderColor="gray.600"
          overflow="hidden"
        >
          <Table.Root size="md" variant="outline">
            <Table.Header bg="gray.700">
              <Table.Row>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Nome
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Tipo
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Data de Criação
                </Table.ColumnHeader>
                <Table.ColumnHeader
                  color="white"
                  fontWeight="bold"
                  display={"flex"}
                  justifyContent={"center"}
                  borderBottom={"1px solid #3f3f46"}
                >
                  Ações
                </Table.ColumnHeader>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {categoriesData.map((item) => (
                <Table.Row key={item.id} _hover={{ bg: "gray.700" }}>
                  <Table.Cell>
                    <Text color="gray.300" fontWeight="medium">
                      {item.name}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300" maxW="200px">
                      {translateCategoryType(item.type)}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300" maxW="200px">
                      {item.createdAt}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <HStack gap={2} justifyContent={"center"}>
                      <DefaultButton
                        tooltipContent="Editar"
                        buttonColor="#156082"
                        size="sm"
                      >
                        <LuPencil />
                      </DefaultButton>
                      <DefaultButton
                        tooltipContent="Excluir"
                        buttonColor="red.500"
                        size="sm"
                      >
                        <LuTrash2 />
                      </DefaultButton>
                    </HStack>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table.Root>
        </Box>
      </Stack>
      <BasicModal
        open={isModalOpen}
        size="sm"
        setOpen={setIsModalOpen}
        cancelText="Cancelar"
        handleDelete={handleCloseModal}
        deleteText="Interromper"
        placement="center"
        isSubmitting={false}
        children={
          <VStack gap={5} m={5}>
            <Text fontSize={"xl"} fontWeight={"bold"} color="red.500">
              Interromper IA
            </Text>
            <Text fontSize={"md"} fontWeight={"bold"}>
              Você tem certeza que deseja interromper a IA? Essa ação não pode
              ser desfeita.
            </Text>
          </VStack>
        }
      />
    </Flex>
  );
}
