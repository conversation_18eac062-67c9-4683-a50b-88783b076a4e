import DrawerMenu from "@/components/global/drawer/drawer-menu";
import NavLinks from "@/components/global/navlinks/navlinks";
import { Flex } from "@chakra-ui/react";
import { Metadata } from "next";

type ManagementLayoutContainerProps = {
  children: React.ReactNode;
};

export const metadata: Metadata = {
  title: "Banco ABC",
  description: "",
  icons: {
    icon: "/favicon.ico",
  },
};

export default function ManagementLayoutContainer({
  children,
}: ManagementLayoutContainerProps) {
  return (
    <Flex w={"100vw"} h={"100vh"} bgColor="rgb(35,34,34)" overflow="hidden">
      <DrawerMenu children={<NavLinks />} />
      <Flex
        flex={1}
        marginLeft={12}
        h="100vh"
        overflow="auto"
        css={{
          "&::-webkit-scrollbar": {
            width: "8px",
          },
          "&::-webkit-scrollbar-track": {
            background: "transparent",
          },
          "&::-webkit-scrollbar-thumb": {
            background: "#555",
            borderRadius: "4px",
          },
          "&::-webkit-scrollbar-thumb:hover": {
            background: "#666",
          },
        }}
      >
        {children}
      </Flex>
    </Flex>
  );
}
