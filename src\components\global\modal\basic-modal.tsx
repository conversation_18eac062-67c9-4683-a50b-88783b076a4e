import { CloseButton, Dialog, HStack, Portal } from "@chakra-ui/react";
import DefaultButton from "../buttons/button";

type BasicModalProps = {
  placement?: "top" | "bottom" | "center";
  open: boolean;
  setOpen: (open: boolean) => void;
  children?: React.ReactNode;
  title?: string;
  cancelText?: string;
  confirmText?: string;
  deleteText?: string;
  handleDelete?: () => void;
  handleConfirm?: () => void;
  asForm?: boolean;
  handleSubmit?: (e: React.FormEvent<HTMLFormElement>) => void;
  isSubmitting?: boolean;
  size?: "sm" | "md" | "lg" | "xl" | "xs" | "cover" | "full";
  closeOnInteractOutside?: boolean;
  closeOnEscape?: boolean;
};

export default function BasicModal({
  placement,
  open,
  setOpen,
  children,
  title,
  cancelText,
  confirmText,
  deleteText,
  handleDelete,
  asForm,
  isSubmitting,
  handleSubmit,
  handleConfirm,
  size,
  closeOnInteractOutside = false,
  closeOnEscape = false,
}: BasicModalProps) {
  return (
    <HStack wrap="wrap" gap="4">
      <Dialog.Root placement={"top"} motionPreset="slide-in-bottom">
        <Dialog.Trigger asChild>
          <DefaultButton variant="outline">
            Open Dialog ({placement})
          </DefaultButton>
        </Dialog.Trigger>
        <Portal>
          <Dialog.Backdrop />
          <Dialog.Positioner>
            <Dialog.Content>
              <Dialog.Header>
                <Dialog.Title>Dialog Title</Dialog.Title>
              </Dialog.Header>
              <Dialog.Body>
                <p>
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed
                  do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                </p>
              </Dialog.Body>
              <Dialog.Footer>
                <Dialog.ActionTrigger asChild>
                  <DefaultButton variant="outline">Cancel</DefaultButton>
                </Dialog.ActionTrigger>
                <DefaultButton>Save</DefaultButton>
              </Dialog.Footer>
              <Dialog.CloseTrigger asChild>
                <CloseButton size="sm" />
              </Dialog.CloseTrigger>
            </Dialog.Content>
          </Dialog.Positioner>
        </Portal>
      </Dialog.Root>
    </HStack>
  );
}
